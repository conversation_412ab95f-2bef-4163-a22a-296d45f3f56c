# Task Master AI 安装指南

## 1. 本地源码安装

### 1.1 克隆项目
```bash
git clone https://github.com/eyaltoledano/claude-task-master.git
cd claude-task-master
npm install
```

### 1.2 验证安装
```bash
# 检查版本
task-master --version

# 查看帮助
task-master --help
```

## 2. Cursor MCP 配置

### 2.1 配置文件位置说明

- **全局MCP配置**：`c:\Users\<USER>\.cursor\mcp.json`（Cursor实际使用）
- **项目MCP配置**：`项目根目录\.cursor\mcp.json`（Task Master检测API密钥状态）

### 2.2 MCP服务器配置

**⚠️ 重要提示**：服务器名称必须是 `taskmaster-ai`（不是 `task-master-ai`），否则API密钥检测会失败。

#### 全局配置示例（使用本地源码）

```json
{
  "mcpServers": {
    "taskmaster-ai": {
      "command": "node",
      "args": ["D:\\task_master\\claude-task-master\\mcp-server\\server.js"],
      "env": {
        "ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE",
        "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "GOOGLE_API_KEY": "YOUR_GOOGLE_KEY_HERE",
        "MISTRAL_API_KEY": "YOUR_MISTRAL_KEY_HERE",
        "OPENROUTER_API_KEY": "YOUR_OPENROUTER_KEY_HERE",
        "XAI_API_KEY": "YOUR_XAI_KEY_HERE",
        "AZURE_OPENAI_API_KEY": "YOUR_AZURE_KEY_HERE"
      }
    }
  }
}
```

#### 项目内配置示例（用于API密钥检测）

在项目根目录创建 `.cursor/mcp.json`：

```json
{
  "mcpServers": {
    "taskmaster-ai": {
      "command": "node",
      "args": ["./mcp-server/server.js"],
      "env": {
        "ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE",
        "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "GOOGLE_API_KEY": "YOUR_GOOGLE_KEY_HERE",
        "MISTRAL_API_KEY": "YOUR_MISTRAL_KEY_HERE",
        "OPENROUTER_API_KEY": "YOUR_OPENROUTER_KEY_HERE",
        "XAI_API_KEY": "YOUR_XAI_KEY_HERE",
        "AZURE_OPENAI_API_KEY": "YOUR_AZURE_KEY_HERE"
      }
    }
  }
}
```

**注意事项**：
- 使用绝对路径（如 `D:\\task_master\\claude-task-master\\mcp-server\\server.js`）可以避免路径解析问题
- 项目配置文件中也需要设置真实的API密钥，因为Task Master的API密钥检测功能会读取这个文件
- 只有您实际拥有的API密钥需要替换为真实值，其他可以保持占位符

### 2.3 重启Cursor
配置完成后重启Cursor以加载MCP服务器。

## 3. 环境变量配置
### 3.1 API密钥配置
在项目根目录创建 `.env` 文件（仅用于API密钥）：
```env
# 根据您配置的模型提供相应的API密钥
ANTHROPIC_API_KEY=your_anthropic_key_here
PERPLEXITY_API_KEY=your_perplexity_key_here
OPENAI_API_KEY=your_openai_key_here
GOOGLE_API_KEY=your_google_key_here
MISTRAL_API_KEY=your_mistral_key_here
OPENROUTER_API_KEY=your_openrouter_key_here
XAI_API_KEY=your_xai_key_here
AZURE_OPENAI_API_KEY=your_azure_key_here
# 可选的端点配置
AZURE_OPENAI_ENDPOINT=https://your-azure-endpoint.openai.azure.com/
OLLAMA_BASE_URL=http://localhost:11434/api
VERTEX_PROJECT_ID=your-gcp-project-id
VERTEX_LOCATION=us-central1
```
### 3.2 配置文件说明
Task Master使用两种配置方式：
1. **`.taskmaster/config.json`**（主要配置）
   - 存储模型选择、参数设置、日志级别等
   - 通过AI助手或CLI命令管理
   - 自动在项目初始化时创建
2. **`.env` 文件**（仅用于API密钥）
   - 仅存储敏感的API密钥
   - CLI使用时必需

## 4. 项目初始化
### 4.1 通过MCP工具初始化（推荐）
在Cursor中与AI对话：
```
Initialize taskmaster-ai in my project
```
### 4.2 通过CLI初始化
```bash
# 在项目目录下，这个命令只适用于本地源码安装，并且在安装的项目目录下运行
node scripts/init.js
# 或在项目目录下运行
task-master init
```
这将创建 `.taskmaster/` 目录结构，包括：
- `.taskmaster/config.json` - 主要配置文件
- `.taskmaster/tasks/` - 任务文件目录
- `.taskmaster/docs/` - 文档目录
- `.taskmaster/reports/` - 报告目录
- `.taskmaster/templates/` - 模板文件目录

## 5. 模型配置
### 5.1 通过AI助手配置（推荐）
在Cursor中与AI对话：
```
Can you help me configure the AI models for Task Master?
Change the main, research and fallback models to gpt-4o, gpt-4o-search-preview and gpt-4o-mini respectively.
```
AI会使用MCP工具帮您配置。
### 5.2 通过CLI配置
```bash
# 查看当前配置和可用模型
task-master models
# 交互式配置模型
task-master models --setup
# 设置特定模型
task-master models --set-main=gpt-4o
task-master models --set-research=gpt-4o-search-preview
task-master models --set-fallback=gpt-4o-mini
# 设置自定义Ollama模型
task-master models --set-main=my-local-llama --ollama
# 设置自定义OpenRouter模型
task-master models --set-research=google/gemini-pro --openrouter
```
### 5.3 配置文件结构
Task Master使用 `.taskmaster/config.json` 文件存储配置：
```json
{
  "models": {
    "main": {
      "provider": "openai",
      "modelId": "gpt-4o",
      "maxTokens": 50000,
      "temperature": 0.2
    },
    "research": {
      "provider": "openai", 
      "modelId": "gpt-4o-search-preview",
      "maxTokens": 8700,
      "temperature": 0.1
    },
    "fallback": {
      "provider": "openai",
      "modelId": "gpt-4o-mini", 
      "maxTokens": 128000,
      "temperature": 0.2
    }
  },
  "global": {
    "logLevel": "info",
    "debug": false,
    "defaultSubtasks": 5,
    "defaultPriority": "medium",
    "projectName": "Your Project Name"
  }
}
```
### 5.4 模型兼容性说明
**重要**：不是所有模型都支持工具调用（function calling），Task Master需要此功能才能正常工作。
**支持工具调用的模型**（推荐）：
- OpenAI: `gpt-4o`, `gpt-4o-mini`, `gpt-4-turbo`, `gpt-3.5-turbo`
- Anthropic: `claude-3-5-sonnet`, `claude-3-opus`, `claude-3-haiku`
- Google: `gemini-pro`, `gemini-1.5-pro`
**不支持工具调用的模型**（会导致错误）：
- OpenAI: `o1-preview`, `o1-mini`, `o3-mini`, `o4-mini`

## 6. 验证安装
### 6.1 检查模型配置
在Cursor中与AI对话：
```
请检查当前的模型配置和API密钥状态
```
或通过CLI：
```bash
# 查看当前模型配置
task-master models
# 查看可用模型列表
task-master models --list-available
# 设置主模型
task-master models --set-main=gpt-4o
# 设置研究模型
task-master models --set-research=gpt-4o-search-preview
# 设置备用模型
task-master models --set-fallback=gpt-4o-mini
# 交互式配置
task-master models --setup
```
确保显示：
- 所有模型的 `keyStatus.cli: true`
- 所有模型的 `keyStatus.mcp: true`
### 6.2 测试AI功能
在Cursor中与AI对话：
```
请添加一个测试任务来验证AI功能是否正常工作
```
或通过CLI：
```bash
# 添加测试任务
task-master add-task --prompt="测试任务：验证AI功能是否正常" --priority=low

# 使用研究模式添加任务
task-master add-task --prompt="测试任务：验证AI功能是否正常" --priority=low --research
```
### 6.3 查看任务列表
在Cursor中与AI对话：
```
请显示所有当前任务
```
或通过CLI：
```bash
# 列出所有任务
task-master list
# 查看特定状态的任务
task-master list --status=pending
task-master list --status=done
task-master list --status=in-progress
# 包含子任务的详细列表
task-master list --with-subtasks
# 指定任务文件
task-master list --file=.taskmaster/tasks/tasks.json
```

## 7. 基本使用

### 7.1 从PRD生成任务
在Cursor中与AI对话：
```
请解析我的PRD文件并生成任务，PRD文件位于 .taskmaster/docs/prd.txt
```

或
```
请使用task-master parse-prd命令从我的PRD生成任务，PRD文件在 .taskmaster/docs/prd.txt
```

或
```
请从PRD生成大约10个顶级任务
```

或通过CLI：
```bash
# 编辑PRD文件
# 编辑 .taskmaster/docs/prd.txt

# 从PRD生成任务
task-master parse-prd .taskmaster/docs/prd.txt

# 限制生成任务数量
task-master parse-prd .taskmaster/docs/prd.txt --num-tasks=10

# 指定输出文件
task-master parse-prd .taskmaster/docs/prd.txt --output=.taskmaster/tasks/tasks.json

# 强制覆盖现有任务
task-master parse-prd .taskmaster/docs/prd.txt --force

# 使用研究模式
task-master parse-prd .taskmaster/docs/prd.txt --research

# 追加到现有任务
task-master parse-prd .taskmaster/docs/prd.txt --append
```

### 7.2 查看任务
在Cursor中与AI对话：
```
有哪些任务可以开始工作？
```

或
```
我下一步应该做哪个任务？请考虑依赖关系和优先级
```

或
```
请显示任务1的详细信息
```

或
```
请显示下一个可以执行的任务
```

或通过CLI：
```bash
# 列出所有任务
task-master list

# 查看特定状态的任务
task-master list --status=pending
task-master list --status=done
task-master list --status=in-progress
task-master list --status=review
task-master list --status=deferred
task-master list --status=cancelled

# 查看任务状态（包含子任务）
task-master list --with-subtasks

# 查看特定任务详情
task-master show 1
task-master show 1.2  # 查看子任务

# 查看下一个可执行任务
task-master next

# 指定任务文件
task-master show 1 --file=.taskmaster/tasks/tasks.json
```

### 7.3 任务管理
在Cursor中与AI对话：
```
我已经完成了任务2中描述的身份验证系统实现，所有测试都通过了。请将其标记为完成，并告诉我下一步应该做什么
```

或
```
请将任务1的状态设置为已完成
```

或
```
我需要添加一个新任务来实现用户注册功能
```

或
```
请将任务3的状态设置为进行中
```

或
```
请删除任务5，它不再需要了
```

或
```
请更新任务1，指定我们需要使用JWT认证
```

或
```
我们决定使用React Query替代Redux，请更新从任务5开始的所有后续任务以反映这个变化
```

或通过CLI：
```bash
# 标记任务完成
task-master set-status --id=2 --status=done

# 设置任务状态为进行中
task-master set-status --id=3 --status=in-progress

# 设置任务状态为审核中
task-master set-status --id=4 --status=review

# 设置任务状态为延期
task-master set-status --id=5 --status=deferred

# 设置任务状态为取消
task-master set-status --id=6 --status=cancelled

# 批量设置状态
task-master set-status --id=1,2,3 --status=done

# 添加新任务
task-master add-task --prompt="实现用户注册功能" --priority=high

# 添加带依赖的任务
task-master add-task --prompt="实现用户注册功能" --dependencies=1,2 --priority=high

# 使用研究模式添加任务
task-master add-task --prompt="实现用户注册功能" --research --priority=high

# 更新特定任务
task-master update-task --id=1 --prompt="需要使用JWT认证"

# 使用研究模式更新任务
task-master update-task --id=1 --prompt="需要使用JWT认证" --research

# 批量更新任务
task-master update --from=5 --prompt="改用React Query替代Redux"

# 使用研究模式批量更新
task-master update --from=5 --prompt="改用React Query替代Redux" --research

# 删除任务
task-master remove-task --id=5

# 强制删除（跳过确认）
task-master remove-task --id=5 --yes

# 批量删除任务
task-master remove-task --id=5,6,7 --yes
```

### 7.4 子任务管理
在Cursor中与AI对话：
```
请为任务1添加一个设计数据库表结构的子任务
```

或
```
请更新子任务1.2，添加更多实现细节
```

或
```
请删除子任务1.2，它不再需要了
```

或
```
请将子任务1.2转换为独立任务
```

或
```
请将任务5移动为任务1的子任务
```

或通过CLI：
```bash
# 添加子任务
task-master add-subtask --id=1 --title="设计数据库表结构"

# 添加带描述和详情的子任务
task-master add-subtask --id=1 --title="设计数据库表结构" --description="设计用户表和权限表" --details="包含字段定义和索引"

# 添加带依赖的子任务
task-master add-subtask --id=1 --title="设计数据库表结构" --dependencies=2,3

# 添加带状态的子任务
task-master add-subtask --id=1 --title="设计数据库表结构" --status=in-progress

# 将现有任务转为子任务
task-master add-subtask --id=1 --task-id=5

# 更新子任务
task-master update-subtask --id=1.2 --prompt="添加实现细节"

# 使用研究模式更新子任务
task-master update-subtask --id=1.2 --prompt="添加实现细节" --research

# 删除子任务
task-master remove-subtask --id=1.2

# 将子任务转为独立任务
task-master remove-subtask --id=1.2 --convert

# 批量删除子任务
task-master remove-subtask --id=1.2,1.3,1.4

# 跳过文件重新生成
task-master add-subtask --id=1 --title="测试子任务" --skip-generate
```

### 7.5 任务分解
在Cursor中与AI对话：
```
请分析所有任务的复杂度，并建议哪些任务需要进一步分解
```

或
```
我想实现任务4，请帮我理解需要做什么以及如何处理
```

或
```
请将任务1展开为更小的子任务
```

或
```
我需要用不同的方法重新生成任务3的子任务，请帮我清除并重新生成
```

或
```
请将所有待处理的任务展开为子任务
```

或
```
请查看复杂度分析报告
```

或通过CLI：
```bash
# 分析任务复杂度
task-master analyze-complexity

# 使用研究模式进行分析
task-master analyze-complexity --research

# 设置复杂度阈值
task-master analyze-complexity --threshold=6

# 分析特定任务范围
task-master analyze-complexity --from=1 --to=5

# 分析特定任务ID
task-master analyze-complexity --ids=1,3,5

# 指定输出文件
task-master analyze-complexity --output=.taskmaster/reports/my-complexity-report.json

# 查看复杂度报告
task-master complexity-report

# 查看特定报告文件
task-master complexity-report --file=.taskmaster/reports/my-complexity-report.json

# 展开复杂任务为子任务
task-master expand --id=1

# 指定子任务数量
task-master expand --id=1 --num=5

# 提供额外上下文
task-master expand --id=1 --prompt="专注于安全考虑"

# 强制重新生成子任务
task-master expand --id=1 --force

# 使用研究模式展开
task-master expand --id=1 --research

# 展开所有待处理任务
task-master expand --all

# 强制重新生成所有任务的子任务
task-master expand --all --force

# 使用研究模式展开所有任务
task-master expand --all --research

# 清除子任务
task-master clear-subtasks --id=1

# 清除多个任务的子任务
task-master clear-subtasks --id=1,2,3

# 清除所有任务的子任务
task-master clear-subtasks --all
```

### 7.6 依赖管理
在Cursor中与AI对话：
```
请添加依赖关系，让任务3依赖于任务1
```

或
```
请移除任务3和任务1之间的依赖关系
```

或
```
请检查当前任务中是否有依赖问题
```

或
```
请自动修复任何依赖问题
```

或通过CLI：
```bash
# 添加依赖关系
task-master add-dependency --id=3 --depends-on=1

# 删除依赖关系
task-master remove-dependency --id=3 --depends-on=1

# 验证依赖关系
task-master validate-dependencies

# 自动修复依赖问题
task-master fix-dependencies

# 指定任务文件
task-master add-dependency --id=3 --depends-on=1 --file=.taskmaster/tasks/tasks.json
```

### 7.7 任务移动和重组
在Cursor中与AI对话：
```
任务8实际上应该是任务4的子任务，请重新组织一下
```

或
```
我刚刚合并了主分支，tasks.json有冲突。我的队友在他们的分支上创建了任务10-15，而我在我的分支上创建了任务10-12。请帮我移动我的任务来解决这个冲突
```

或
```
请将任务5移动到位置3
```

或
```
请将子任务3.2转换为位置8的独立任务
```

或通过CLI：
```bash
# 移动任务位置
task-master move --from=5 --to=3

# 将任务转为子任务
task-master move --from=5 --to=3.1

# 将子任务转为独立任务
task-master move --from=3.2 --to=8

# 移动子任务到不同父任务
task-master move --from=5.2 --to=7.3

# 批量移动任务
task-master move --from=10,11,12 --to=16,17,18

# 重新排序子任务
task-master move --from=5.2 --to=5.4

# 指定任务文件
task-master move --from=5 --to=3 --file=.taskmaster/tasks/tasks.json
```

### 7.8 生成任务文件
在Cursor中与AI对话：
```
请为所有任务生成Markdown文件
```

或通过CLI：
```bash
# 生成Markdown任务文件
task-master generate

# 指定输出目录
task-master generate --output=./my-tasks

# 指定任务文件
task-master generate --file=.taskmaster/tasks/tasks.json
```

## 8. 高级功能

### 8.1 研究增强功能
在Cursor中与AI对话：
```
请使用研究模式添加一个实现OAuth认证的任务，以获得最佳实践建议
```

或
```
请更新任务1以集成第三方支付，使用研究支持的建议
```

或
```
请使用研究模式展开任务1，以获得最全面的分解
```

或
```
请使用研究模式分析任务复杂度，以获得更准确的评估
```

或通过CLI：
```bash
# 使用研究功能添加任务
task-master add-task --prompt="实现OAuth认证" --research

# 使用研究功能更新任务
task-master update-task --id=1 --prompt="集成第三方支付" --research

# 使用研究功能批量更新
task-master update --from=5 --prompt="集成第三方支付" --research

# 使用研究功能更新子任务
task-master update-subtask --id=1.2 --prompt="添加安全考虑" --research

# 使用研究功能展开任务
task-master expand --id=1 --research

# 使用研究功能展开所有任务
task-master expand --all --research

# 使用研究功能分析复杂度
task-master analyze-complexity --research

# 使用研究功能解析PRD
task-master parse-prd .taskmaster/docs/prd.txt --research
```

### 8.2 项目迁移

在Cursor中与AI对话：
```
我有一个旧的Task Master项目，请帮我迁移到新的目录结构
```

或
```
请预览从我的旧Task Master配置中会迁移什么内容
```

或
```
请迁移我的项目并创建旧文件的备份
```

如果您有使用旧版本Task Master的项目，可以迁移到新的目录结构：

```bash
# 预览迁移内容
task-master migrate --dry-run

# 执行迁移并备份
task-master migrate --backup

# 强制迁移（覆盖现有文件）
task-master migrate --force

# 迁移后清理旧文件
task-master migrate --cleanup

# 指定源目录
task-master migrate --source=./old-project

# 指定目标目录
task-master migrate --target=./new-project

# 详细输出迁移过程
task-master migrate --verbose
```

## 9. 开发工作流程

### 9.1 AI驱动开发示例

在Cursor中，你可以直接与Task Master AI对话：

```
用户：我需要为我的电商网站添加购物车功能

AI：我来帮你分析并创建购物车功能的任务。首先让我查看当前的任务状态，然后为你创建相关任务。

[AI使用get_tasks工具查看当前任务]
[AI使用add_task工具创建购物车功能任务]
[AI使用expand_task工具将任务分解为子任务]

用户：购物车需要支持优惠券功能

AI：好的，我来更新购物车任务，添加优惠券功能的要求。

[AI使用update_task工具更新任务内容]
[AI使用add_subtask工具添加优惠券相关子任务]
[AI使用expand_task工具将任务分解为子任务]

用户：购物车需要支持优惠券功能

AI：好的，我来更新购物车任务，添加优惠券功能的要求。

[AI使用update_task工具更新任务内容]
[AI使用add_subtask工具添加优惠券相关子任务]
```

### 9.2 常用状态值

Task Master支持以下任务状态：

- `pending` - 待处理（默认状态）
- `in-progress` - 进行中
- `done` - 已完成
- `review` - 审核中
- `deferred` - 延期
- `cancelled` - 已取消
- `blocked` - 被阻塞

### 9.3 优先级设置

支持的优先级级别：

- `high` - 高优先级
- `medium` - 中等优先级（默认）
- `low` - 低优先级

### 9.4 文件路径说明

Task Master使用以下目录结构：

```
.taskmaster/
├── config.json          # 主配置文件
├── docs/
│   ├── prd.txt          # 产品需求文档
│   └── templates/       # 模板文件
├── tasks/
│   ├── tasks.json       # 主任务文件
│   └── *.md            # 生成的任务Markdown文件
└── reports/
    └── task-complexity-report.json  # 复杂度分析报告
```

### 9.5 任务状态管理流程

1. **查看下一个任务**：
   - 对话：`我下一步应该做哪个任务？`
   - CLI：`task-master next`

2. **开始工作**：
   - 对话：`请将任务X的状态设置为进行中`
   - CLI：`task-master set-status --id=X --status=in-progress`

3. **实现功能**：编写代码

4. **更新进度**：
   - 对话：`我已经完成了任务X.Y的数据库设计部分，请记录这个进度`
   - CLI：`task-master update-subtask --id=X.Y --prompt="完成数据库设计"`

5. **完成任务**：
   - 对话：`任务X已经完成，所有测试都通过了，请标记为完成`
   - CLI：`task-master set-status --id=X --status=done`

6. **生成文档**：
   - 对话：`请为所有任务生成Markdown文件`
   - CLI：`task-master generate`

### 9.6 处理实现偏差

当实现过程中发现需要调整方向时：

在Cursor中与AI对话：
```
我们决定改用MongoDB替代PostgreSQL，请更新从任务10开始的所有后续任务
```

或通过CLI：
```bash
# 更新后续任务以反映新的技术选择
task-master update --from=10 --prompt="改用MongoDB替代PostgreSQL"

# 使用研究模式获取最佳实践
task-master update --from=10 --prompt="更新为使用MongoDB，研究最佳实践" --research
```

## 10. 常见问题解决

### 10.1 API密钥检测失败

**问题**：`task-master models` 显示 `mcp: false`

**解决方案**：
1. 检查服务器名称是否为 `taskmaster-ai`
2. 确保项目内存在 `.cursor/mcp.json` 文件
3. 验证API密钥是否正确配置

### 10.2 模型不支持工具调用

**问题**：MCP工具调用失败

**解决方案**：
1. 使用支持工具调用的模型（如 `gpt-4o`、`claude-3-5-sonnet`）
2. 避免使用 `o3-mini`、`o4-mini` 等模型
3. 运行 `task-master models --setup` 重新配置

### 10.3 Node.js模块解析错误

**问题**：`ERR_MODULE_NOT_FOUND` 错误

**解决方案**：
1. 清理npm缓存：`npm cache clean --force`
2. 重新安装依赖：`rm -rf node_modules && npm install`
3. 使用PowerShell或CMD替代Git Bash

### 10.4 JSON解析错误

**问题**：`SyntaxError: Unexpected end of JSON input`

**解决方案**：
1. 在PowerShell或CMD中运行命令
2. 检查MCP配置文件格式是否正确
3. 重启Cursor重新加载MCP服务器

### 10.5 配置文件问题

**问题**：配置文件找不到或格式错误

**解决方案**：
1. 运行 `task-master models --setup` 重新创建配置
2. 确保使用新的 `.taskmaster/config.json` 结构
3. 如有旧的 `.taskmasterconfig` 文件，运行 `task-master migrate` 迁移

## 11. 最佳实践

### 11.1 任务组织
- 使用清晰的任务标题和描述
- 合理设置任务依赖关系
- 定期运行复杂度分析
- 及时更新任务状态

### 11.2 配置管理
- 定期备份 `.taskmaster/config.json`
- 使用环境变量管理敏感信息
- 为不同项目使用不同的配置

### 11.3 团队协作
- 使用 `task-master move` 解决任务ID冲突
- 定期同步任务状态
- 使用 `task-master validate-dependencies` 检查依赖完整性

### 11.4 版本控制
- 将 `.taskmaster/` 目录加入版本控制
- 将 `.env` 文件加入 `.gitignore`
- 将项目内的 `.cursor/mcp.json` 加入 `.gitignore`

---

## 版本信息

- Task Master AI版本：0.16.1+
- Node.js要求：16.0+
- 支持的AI提供商：OpenAI、Anthropic、Perplexity、Google、Mistral、xAI、OpenRouter、Azure OpenAI、Ollama

---