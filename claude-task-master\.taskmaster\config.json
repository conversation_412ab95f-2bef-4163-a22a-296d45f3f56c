{"models": {"main": {"provider": "openai", "modelId": "gpt-4o", "maxTokens": 50000, "temperature": 0.2}, "research": {"provider": "openai", "modelId": "gpt-4o-search-preview", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "openai", "modelId": "gpt-4o-mini", "maxTokens": 128000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "userId": "**********", "azureBaseURL": "https://your-endpoint.azure.com/", "defaultTag": "master"}}