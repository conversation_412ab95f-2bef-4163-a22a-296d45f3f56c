Display the task complexity analysis report.

Arguments: $ARGUMENTS

View the detailed complexity analysis generated by analyze-complexity command.

## Viewing Complexity Report

Shows comprehensive task complexity analysis with actionable insights.

## Execution

```bash
task-master complexity-report [--file=<path>]
```

## Report Location

Default: `.taskmaster/reports/complexity-analysis.md`
Custom: Specify with --file parameter

## Report Contents

### 1. **Executive Summary**
```
Complexity Analysis Summary
━━━━━━━━━━━━━━━━━━━━━━━━
Analysis Date: 2024-01-15
Tasks Analyzed: 32
High Complexity: 5 (16%)
Medium Complexity: 12 (37%)
Low Complexity: 15 (47%)

Critical Findings:
- 5 tasks need immediate expansion
- 3 tasks have high technical risk
- 2 tasks block critical path
```

### 2. **Detailed Task Analysis**
For each complex task:
- Complexity score breakdown
- Contributing factors
- Specific risks identified
- Expansion recommendations
- Similar completed tasks

### 3. **Risk Matrix**
Visual representation:
```
Risk vs Complexity Matrix
━━━━━━━━━━━━━━━━━━━━━━━
High Risk  | #5(9) #12(8) | #23(6)
Med Risk   | #34(7)       | #45(5) #67(5)
Low Risk   | #78(8)       | [15 tasks]
           | High Complex  | Med Complex
```

### 4. **Recommendations**

**Immediate Actions:**
1. Expand task #5 - Critical path + high complexity
2. Expand task #12 - High risk + dependencies
3. Review task #34 - Consider splitting

**Sprint Planning:**
- Don't schedule multiple high-complexity tasks together
- Ensure expertise available for complex tasks
- Build in buffer time for unknowns

## Interactive Features

When viewing report:
1. **Quick Actions**
   - Press 'e' to expand a task
   - Press 'd' for task details
   - Press 'r' to refresh analysis

2. **Filtering**
   - View by complexity level
   - Filter by risk factors
   - Show only actionable items

3. **Export Options**
   - Markdown format
   - CSV for spreadsheets
   - JSON for tools

## Report Intelligence

- Compares with historical data
- Shows complexity trends
- Identifies patterns
- Suggests process improvements

## Integration

Use report for:
- Sprint planning sessions
- Resource allocation
- Risk assessment
- Team discussions
- Client updates

## Example Usage

```
/project:tm/complexity-report
→ Opens latest analysis

/project:tm/complexity-report --file=archived/2024-01-01.md
→ View historical analysis

After viewing:
/project:tm/expand 5
→ Expand high-complexity task
```